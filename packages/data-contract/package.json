{"name": "data-contract", "version": "1.0.0", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc -b", "validate-contract": "npm run build && node dist/validate-contract.js", "test": "jest"}, "dependencies": {"js-yaml": "^4.1.0", "@modelcontextprotocol/sdk": "^1.17.1"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/js-yaml": "^4.0.9", "@types/node": "^20.12.12", "jest": "^29.7.0", "ts-jest": "^29.1.2", "typescript": "^5.4.5"}}